import React, { Fragment, useEffect } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { motion } from 'framer-motion';
import { useRouter } from 'next/router';
import { useTheme } from '@/themes';
import { XMarkIcon } from '@heroicons/react/24/outline';

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  subtitle?: string;
  description?: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg';
  showCloseButton?: boolean;
  closeOnOverlayClick?: boolean;
  className?: string;
}

const AuthModal: React.FC<AuthModalProps> = ({
  isOpen,
  onClose,
  title,
  subtitle,
  description,
  children,
  size = 'md',
  showCloseButton = true,
  closeOnOverlayClick = true,
  className = ''
}) => {
  const { currentTheme, themeName } = useTheme();
  const router = useRouter();
  const { locale } = router;
  const isRTL = locale === 'ar';
  const isGoldTheme = themeName === 'gold';

  // Prevent body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  const sizeClasses = {
    sm: 'max-w-md',
    md: 'max-w-lg',
    lg: 'max-w-2xl',
  };

  const modalBackgroundStyle = {
    background: isGoldTheme 
      ? 'linear-gradient(135deg, rgba(0, 0, 0, 0.95) 0%, rgba(20, 20, 20, 0.98) 50%, rgba(0, 0, 0, 0.95) 100%)'
      : 'linear-gradient(135deg, rgba(0, 0, 0, 0.95) 0%, rgba(15, 15, 30, 0.98) 50%, rgba(0, 0, 0, 0.95) 100%)',
    backdropFilter: 'blur(24px)',
    WebkitBackdropFilter: 'blur(24px)',
    border: '1px solid rgba(255, 255, 255, 0.15)',
    boxShadow: isGoldTheme
      ? '0 25px 50px -12px rgba(251, 191, 36, 0.25), 0 0 0 1px rgba(251, 191, 36, 0.1)'
      : '0 25px 50px -12px rgba(147, 51, 234, 0.25), 0 0 0 1px rgba(147, 51, 234, 0.1)',
  };

  const overlayStyle = {
    background: 'rgba(0, 0, 0, 0.8)',
    backdropFilter: 'blur(8px)',
    WebkitBackdropFilter: 'blur(8px)',
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog
        as="div"
        className="relative z-50"
        onClose={closeOnOverlayClick ? onClose : () => {}}
        dir={isRTL ? 'rtl' : 'ltr'}
      >
        {/* Overlay */}
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div 
            className="fixed inset-0"
            style={overlayStyle}
          />
        </Transition.Child>

        {/* Modal Container */}
        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel
                className={`
                  w-full ${sizeClasses[size]} transform overflow-hidden rounded-2xl
                  text-left align-middle shadow-xl transition-all relative
                  ${className}
                `}
                style={modalBackgroundStyle}
              >
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: 20 }}
                  transition={{ duration: 0.3 }}
                  className="relative"
                >
                  {/* Close Button */}
                  {showCloseButton && (
                    <motion.button
                      type="button"
                      onClick={onClose}
                      className={`
                        absolute top-6 z-10 p-2 rounded-full transition-all duration-200
                        ${isRTL ? 'left-6' : 'right-6'}
                        text-white/60 hover:text-white hover:bg-white/10
                        focus:outline-none focus:ring-2 focus:ring-white/20
                      `}
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      <XMarkIcon className="w-6 h-6" />
                    </motion.button>
                  )}

                  {/* Header */}
                  <div className={`px-8 pt-8 pb-6 ${isRTL ? 'text-right' : 'text-left'}`}>
                    <Dialog.Title
                      as="h3"
                      className={`
                        text-3xl font-bold mb-3
                        ${isRTL ? 'font-cairo text-arabic-premium' : 'font-sans'}
                      `}
                      style={{ color: currentTheme.colors.text.primary }}
                    >
                      {title}
                    </Dialog.Title>

                    {subtitle && (
                      <motion.p
                        className={`
                          text-lg font-medium mb-2
                          ${isRTL ? 'font-tajawal' : 'font-sans'}
                        `}
                        style={{ color: currentTheme.colors.text.accent }}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.1 }}
                      >
                        {subtitle}
                      </motion.p>
                    )}

                    {description && (
                      <motion.p
                        className={`
                          text-base leading-relaxed
                          ${isRTL ? 'font-tajawal' : 'font-sans'}
                        `}
                        style={{ color: currentTheme.colors.text.secondary }}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.2 }}
                      >
                        {description}
                      </motion.p>
                    )}
                  </div>

                  {/* Content */}
                  <div className="px-8 pb-8">
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3, duration: 0.4 }}
                    >
                      {children}
                    </motion.div>
                  </div>

                  {/* Decorative Elements */}
                  <div className="absolute top-0 left-0 w-full h-1 opacity-60">
                    <div
                      className="h-full rounded-t-2xl"
                      style={{
                        background: isGoldTheme
                          ? 'linear-gradient(90deg, transparent 0%, rgba(251, 191, 36, 0.8) 50%, transparent 100%)'
                          : 'linear-gradient(90deg, transparent 0%, rgba(147, 51, 234, 0.8) 50%, transparent 100%)'
                      }}
                    />
                  </div>

                  {/* Shimmer Effect */}
                  <div className="absolute inset-0 opacity-0 hover:opacity-100 transition-opacity duration-500 pointer-events-none rounded-2xl overflow-hidden">
                    <div
                      className="absolute inset-0 opacity-20"
                      style={{
                        background: isGoldTheme
                          ? 'linear-gradient(45deg, transparent 30%, rgba(251, 191, 36, 0.1) 50%, transparent 70%)'
                          : 'linear-gradient(45deg, transparent 30%, rgba(147, 51, 234, 0.1) 50%, transparent 70%)',
                        animation: 'shimmer 3s ease-in-out infinite'
                      }}
                    />
                  </div>
                </motion.div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};

export default AuthModal;
