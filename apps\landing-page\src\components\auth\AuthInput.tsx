import React, { useState, forwardRef } from 'react';
import { motion } from 'framer-motion';
import { useRouter } from 'next/router';
import { useTheme } from '@/themes';
import {
  EyeIcon,
  EyeSlashIcon,
  ExclamationCircleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

interface AuthInputProps {
  label: string;
  placeholder?: string;
  type?: 'text' | 'email' | 'password' | 'tel';
  value: string;
  onChange: (value: string) => void;
  onBlur?: () => void;
  error?: string;
  touched?: boolean;
  required?: boolean;
  disabled?: boolean;
  autoComplete?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  onRightIconClick?: () => void;
  className?: string;
  showPasswordStrength?: boolean;
  passwordStrength?: {
    score: number;
    label: string;
  };
}

const AuthInput = forwardRef<HTMLInputElement, AuthInputProps>(({
  label,
  placeholder,
  type = 'text',
  value,
  onChange,
  error,
  touched = false,
  required = false,
  disabled = false,
  autoComplete,
  leftIcon,
  rightIcon,
  onRightIconClick,
  className = '',
  showPasswordStrength = false,
  passwordStrength
}, ref) => {
  const [showPassword, setShowPassword] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const { currentTheme, themeName } = useTheme();
  const router = useRouter();
  const { locale } = router;
  const isRTL = locale === 'ar';
  const isGoldTheme = themeName === 'gold';

  const hasError = touched && error;
  const hasValue = value.length > 0;
  const isPasswordType = type === 'password';

  const inputType = isPasswordType && showPassword ? 'text' : type;

  const containerClasses = `
    relative w-full mb-6 group
    ${className}
  `;

  const labelClasses = `
    absolute transition-all duration-300 pointer-events-none
    ${isRTL ? 'right-4 font-tajawal' : 'left-4 font-sans'}
    ${hasValue || isFocused 
      ? `top-2 text-xs ${isRTL ? 'right-3' : 'left-3'} px-2 rounded-md`
      : 'top-4 text-base'
    }
    ${hasError 
      ? 'text-red-400' 
      : isFocused 
        ? currentTheme.colors.text.accent 
        : currentTheme.colors.text.secondary
    }
  `;

  const inputClasses = `
    w-full h-14 px-4 pt-6 pb-2 rounded-xl transition-all duration-300
    ${isRTL ? 'text-right font-tajawal pr-4' : 'text-left font-sans pl-4'}
    ${leftIcon ? (isRTL ? 'pr-12' : 'pl-12') : ''}
    ${rightIcon || isPasswordType ? (isRTL ? 'pl-12' : 'pr-12') : ''}
    text-white placeholder-transparent
    focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent
    disabled:opacity-50 disabled:cursor-not-allowed
    ${hasError 
      ? 'border-2 border-red-400 focus:ring-red-400' 
      : `border border-white/20 focus:ring-2 ${
          isGoldTheme 
            ? 'focus:ring-amber-400/50 focus:border-amber-400/50' 
            : 'focus:ring-purple-400/50 focus:border-purple-400/50'
        }`
    }
  `;

  const backgroundStyle = {
    background: hasError 
      ? 'rgba(239, 68, 68, 0.1)' 
      : 'rgba(255, 255, 255, 0.08)',
    backdropFilter: 'blur(20px)',
    WebkitBackdropFilter: 'blur(20px)',
  };

  const labelBackgroundStyle = (hasValue || isFocused) ? {
    background: isGoldTheme 
      ? 'linear-gradient(135deg, rgba(251, 191, 36, 0.1), rgba(245, 158, 11, 0.1))'
      : 'linear-gradient(135deg, rgba(147, 51, 234, 0.1), rgba(126, 34, 206, 0.1))',
    backdropFilter: 'blur(10px)',
    WebkitBackdropFilter: 'blur(10px)',
  } : {};

  const getPasswordStrengthColor = (score: number) => {
    if (score < 2) return 'bg-red-500';
    if (score < 3) return 'bg-yellow-500';
    if (score < 4) return 'bg-blue-500';
    return 'bg-green-500';
  };

  const getPasswordStrengthWidth = (score: number) => {
    return `${(score / 4) * 100}%`;
  };

  return (
    <motion.div
      className={containerClasses}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="relative">
        {/* Left Icon */}
        {leftIcon && (
          <div className={`absolute top-1/2 transform -translate-y-1/2 ${
            isRTL ? 'right-4' : 'left-4'
          } text-white/60 z-10`}>
            {leftIcon}
          </div>
        )}

        {/* Input Field */}
        <input
          ref={ref}
          type={inputType}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          placeholder={placeholder}
          required={required}
          disabled={disabled}
          autoComplete={autoComplete}
          className={inputClasses}
          style={backgroundStyle}
          dir={isRTL ? 'rtl' : 'ltr'}
        />

        {/* Floating Label */}
        <label
          className={labelClasses}
          style={labelBackgroundStyle}
        >
          {label}
          {required && <span className="text-red-400 ml-1">*</span>}
        </label>

        {/* Right Icon / Password Toggle */}
        <div className={`absolute top-1/2 transform -translate-y-1/2 ${
          isRTL ? 'left-4' : 'right-4'
        } z-10`}>
          {isPasswordType ? (
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="text-white/60 hover:text-white transition-colors duration-200 p-1"
              tabIndex={-1}
            >
              {showPassword ? (
                <EyeSlashIcon className="w-5 h-5" />
              ) : (
                <EyeIcon className="w-5 h-5" />
              )}
            </button>
          ) : rightIcon ? (
            <button
              type="button"
              onClick={onRightIconClick}
              className="text-white/60 hover:text-white transition-colors duration-200 p-1"
              tabIndex={-1}
            >
              {rightIcon}
            </button>
          ) : null}
        </div>

        {/* Status Icon */}
        {touched && (
          <div className={`absolute top-1/2 transform -translate-y-1/2 ${
            isRTL ? 'left-12' : 'right-12'
          } z-10`}>
            {hasError ? (
              <ExclamationCircleIcon className="w-5 h-5 text-red-400" />
            ) : value && !hasError ? (
              <CheckCircleIcon className="w-5 h-5 text-green-400" />
            ) : null}
          </div>
        )}
      </div>

      {/* Password Strength Indicator */}
      {showPasswordStrength && passwordStrength && value && (
        <motion.div
          className="mt-2"
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          transition={{ duration: 0.3 }}
        >
          <div className="flex items-center gap-2 mb-1">
            <div className="flex-1 h-1 bg-white/20 rounded-full overflow-hidden">
              <div
                className={`h-full transition-all duration-300 ${getPasswordStrengthColor(passwordStrength.score)}`}
                style={{ width: getPasswordStrengthWidth(passwordStrength.score) }}
              />
            </div>
            <span className={`text-xs ${isRTL ? 'font-tajawal' : 'font-sans'}`}
                  style={{ color: currentTheme.colors.text.secondary }}>
              {passwordStrength.label}
            </span>
          </div>
        </motion.div>
      )}

      {/* Error Message */}
      {hasError && (
        <motion.div
          className={`mt-2 text-sm text-red-400 ${isRTL ? 'text-right font-tajawal' : 'text-left font-sans'}`}
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          transition={{ duration: 0.3 }}
        >
          {error}
        </motion.div>
      )}
    </motion.div>
  );
});

AuthInput.displayName = 'AuthInput';

export default AuthInput;
