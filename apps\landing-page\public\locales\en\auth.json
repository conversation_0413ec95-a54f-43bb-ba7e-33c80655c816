{"modal": {"close": "Close", "loading": "Loading...", "success": "Success", "error": "Error occurred"}, "login": {"title": "Sign In", "subtitle": "Welcome back to Freela Syria", "description": "Sign in to access your account and enjoy all our services", "email": "Email Address", "emailPlaceholder": "Enter your email address", "password": "Password", "passwordPlaceholder": "Enter your password", "rememberMe": "Remember me", "forgotPassword": "Forgot password?", "loginButton": "Sign In", "loggingIn": "Signing in...", "noAccount": "Don't have an account?", "signUp": "Create new account", "loginWith": "Or sign in with", "google": "Google", "facebook": "Facebook", "success": "Successfully signed in", "error": "Invalid email or password", "networkError": "Connection error, please try again"}, "register": {"title": "Create New Account", "subtitle": "Join the Syrian freelancers community", "description": "Create your account now and start your professional journey with the best experts in Syria", "firstName": "First Name", "firstNamePlaceholder": "Enter your first name", "lastName": "Last Name", "lastNamePlaceholder": "Enter your last name", "email": "Email Address", "emailPlaceholder": "Enter your email address", "phone": "Phone Number (Optional)", "phonePlaceholder": "Enter your phone number", "password": "Password", "passwordPlaceholder": "Enter a strong password", "confirmPassword": "Confirm Password", "confirmPasswordPlaceholder": "Re-enter your password", "role": "Account Type", "client": "Client - Looking for services", "expert": "Expert - Providing services", "location": "Location", "governorate": "Governorate", "governoratePlaceholder": "Select your governorate", "city": "City", "cityPlaceholder": "Select your city", "language": "Preferred Language", "arabic": "Arabic", "english": "English", "acceptTerms": "I agree to the", "termsOfService": "Terms of Service", "and": "and", "privacyPolicy": "Privacy Policy", "registerButton": "Create Account", "registering": "Creating account...", "haveAccount": "Already have an account?", "signIn": "Sign In", "success": "Account created successfully", "emailExists": "Email address already in use", "weakPassword": "Password is too weak", "passwordMismatch": "Passwords do not match", "networkError": "Connection error, please try again"}, "validation": {"required": "This field is required", "email": "Please enter a valid email address", "phone": "Please enter a valid phone number", "password": "Password must be at least 8 characters", "passwordStrength": "Password must contain uppercase, lowercase, number and special character", "confirmPassword": "Passwords do not match", "minLength": "Must be at least {{min}} characters", "maxLength": "Must be at most {{max}} characters", "acceptTerms": "You must accept the terms and conditions"}, "passwordStrength": {"weak": "Weak", "fair": "Fair", "good": "Good", "strong": "Strong"}, "socialLogin": {"continueWith": "Or continue with", "google": "Continue with Google", "facebook": "Continue with Facebook", "apple": "Continue with Apple"}}