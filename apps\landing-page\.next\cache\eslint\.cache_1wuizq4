[{"C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\Layout\\Footer.tsx": "1", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\Layout\\Header.tsx": "2", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\Layout\\index.tsx": "3", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\About.tsx": "4", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Contact.tsx": "5", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Features.tsx": "6", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Hero.tsx": "7", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\HowItWorks.tsx": "8", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Newsletter.tsx": "9", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Pricing.tsx": "10", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Testimonials.tsx": "11", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\pages\\index.tsx": "12", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\pages\\_app.tsx": "13", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\pages\\_document.tsx": "14", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ErrorBoundary.tsx": "15", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ui\\GlassButton.tsx": "16", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ui\\GlassCard.tsx": "17", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\utils\\errorFilter.ts": "18", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ui\\SyrianFlag.tsx": "19", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ThemeController\\index.tsx": "20", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\themes\\gold-theme.ts": "21", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\themes\\index.tsx": "22", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\themes\\purple-theme.ts": "23", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\themes\\theme-utils.ts": "24", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\themes\\types.ts": "25", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\auth\\AuthInput.tsx": "26", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\auth\\AuthModal.tsx": "27", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\auth\\index.ts": "28", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\auth\\SignInModal.tsx": "29", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\auth\\SignUpModal.tsx": "30", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\types\\auth.ts": "31"}, {"size": 18375, "mtime": 1749658821771, "results": "32", "hashOfConfig": "33"}, {"size": 23363, "mtime": 1749679808123, "results": "34", "hashOfConfig": "33"}, {"size": 385, "mtime": 1749589496137, "results": "35", "hashOfConfig": "33"}, {"size": 21052, "mtime": 1749660702590, "results": "36", "hashOfConfig": "33"}, {"size": 24992, "mtime": 1749652496189, "results": "37", "hashOfConfig": "33"}, {"size": 14605, "mtime": 1749656490671, "results": "38", "hashOfConfig": "33"}, {"size": 16416, "mtime": 1749679817984, "results": "39", "hashOfConfig": "33"}, {"size": 16384, "mtime": 1749658840369, "results": "40", "hashOfConfig": "33"}, {"size": 15927, "mtime": 1749660219022, "results": "41", "hashOfConfig": "33"}, {"size": 22281, "mtime": 1749659106205, "results": "42", "hashOfConfig": "33"}, {"size": 16498, "mtime": 1749659136828, "results": "43", "hashOfConfig": "33"}, {"size": 3319, "mtime": 1749593558895, "results": "44", "hashOfConfig": "33"}, {"size": 2845, "mtime": 1749647582298, "results": "45", "hashOfConfig": "33"}, {"size": 1646, "mtime": 1749589446276, "results": "46", "hashOfConfig": "33"}, {"size": 2677, "mtime": 1749597314087, "results": "47", "hashOfConfig": "33"}, {"size": 2428, "mtime": 1749596227387, "results": "48", "hashOfConfig": "33"}, {"size": 989, "mtime": 1749596212722, "results": "49", "hashOfConfig": "33"}, {"size": 2120, "mtime": 1749597019188, "results": "50", "hashOfConfig": "33"}, {"size": 984, "mtime": 1749660691021, "results": "51", "hashOfConfig": "33"}, {"size": 9033, "mtime": 1749651379121, "results": "52", "hashOfConfig": "33"}, {"size": 5749, "mtime": 1749647330487, "results": "53", "hashOfConfig": "33"}, {"size": 4060, "mtime": 1749651461056, "results": "54", "hashOfConfig": "33"}, {"size": 5846, "mtime": 1749647373011, "results": "55", "hashOfConfig": "33"}, {"size": 5926, "mtime": 1749647406588, "results": "56", "hashOfConfig": "33"}, {"size": 3583, "mtime": 1749647289790, "results": "57", "hashOfConfig": "33"}, {"size": 7823, "mtime": 1749679248845, "results": "58", "hashOfConfig": "33"}, {"size": 8359, "mtime": 1749679762743, "results": "59", "hashOfConfig": "33"}, {"size": 247, "mtime": 1749679502433, "results": "60", "hashOfConfig": "33"}, {"size": 8565, "mtime": 1749680139757, "results": "61", "hashOfConfig": "33"}, {"size": 32711, "mtime": 1749680150294, "results": "62", "hashOfConfig": "33"}, {"size": 3182, "mtime": 1749679793471, "results": "63", "hashOfConfig": "33"}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ody2rz", {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\Layout\\Footer.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\Layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\Layout\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\About.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Contact.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Features.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Hero.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\HowItWorks.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Newsletter.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Pricing.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Testimonials.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\pages\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\pages\\_app.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\pages\\_document.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ErrorBoundary.tsx", [], ["157"], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ui\\GlassButton.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ui\\GlassCard.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\utils\\errorFilter.ts", ["158", "159", "160", "161", "162", "163", "164", "165"], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ui\\SyrianFlag.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ThemeController\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\themes\\gold-theme.ts", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\themes\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\themes\\purple-theme.ts", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\themes\\theme-utils.ts", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\themes\\types.ts", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\auth\\AuthInput.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\auth\\AuthModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\auth\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\auth\\SignInModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\auth\\SignUpModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\types\\auth.ts", [], [], {"ruleId": "166", "severity": 1, "message": "167", "line": 37, "column": 7, "nodeType": "168", "messageId": "169", "endLine": 37, "endColumn": 20, "suggestions": "170", "suppressions": "171"}, {"ruleId": "166", "severity": 1, "message": "167", "line": 7, "column": 30, "nodeType": "168", "messageId": "169", "endLine": 7, "endColumn": 43}, {"ruleId": "166", "severity": 1, "message": "167", "line": 8, "column": 29, "nodeType": "168", "messageId": "169", "endLine": 8, "endColumn": 41}, {"ruleId": "166", "severity": 1, "message": "167", "line": 37, "column": 5, "nodeType": "168", "messageId": "169", "endLine": 37, "endColumn": 18}, {"ruleId": "172", "severity": 1, "message": "173", "line": 37, "column": 31, "nodeType": "174", "messageId": "175", "endLine": 37, "endColumn": 34, "suggestions": "176"}, {"ruleId": "166", "severity": 1, "message": "167", "line": 44, "column": 5, "nodeType": "168", "messageId": "169", "endLine": 44, "endColumn": 17}, {"ruleId": "172", "severity": 1, "message": "173", "line": 44, "column": 30, "nodeType": "174", "messageId": "175", "endLine": 44, "endColumn": 33, "suggestions": "177"}, {"ruleId": "166", "severity": 1, "message": "167", "line": 73, "column": 3, "nodeType": "168", "messageId": "169", "endLine": 73, "endColumn": 16}, {"ruleId": "166", "severity": 1, "message": "167", "line": 74, "column": 3, "nodeType": "168", "messageId": "169", "endLine": 74, "endColumn": 15}, "no-console", "Unexpected console statement.", "MemberExpression", "unexpected", ["178"], ["179"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["180", "181"], ["182", "183"], {"messageId": "184", "data": "185", "fix": "186", "desc": "187"}, {"kind": "188", "justification": "189"}, {"messageId": "190", "fix": "191", "desc": "192"}, {"messageId": "193", "fix": "194", "desc": "195"}, {"messageId": "190", "fix": "196", "desc": "192"}, {"messageId": "193", "fix": "197", "desc": "195"}, "removeConsole", {"propertyName": "198"}, {"range": "199", "text": "189"}, "Remove the console.error().", "directive", "", "suggestUnknown", {"range": "200", "text": "201"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "202", "text": "203"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "204", "text": "201"}, {"range": "205", "text": "203"}, "error", [1086, 1152], [1100, 1103], "unknown", [1100, 1103], "never", [1286, 1289], [1286, 1289]]