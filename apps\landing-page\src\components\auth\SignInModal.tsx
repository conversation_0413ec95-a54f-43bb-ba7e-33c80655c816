import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useRouter } from 'next/router';
import { useTranslation } from 'next-i18next';
import { useTheme } from '@/themes';
import { userLoginSchema } from '@freela/utils';
import { z } from 'zod';
import AuthModal from './AuthModal';
import AuthInput from './AuthInput';
import { LoginFormData, AuthModalProps } from '@/types/auth';
import {
  EnvelopeIcon,
  LockClosedIcon,
  ArrowRightIcon,
  ArrowLeftIcon
} from '@heroicons/react/24/outline';

interface SignInModalProps extends AuthModalProps {
  onSwitchToSignUp: () => void;
}

const SignInModal: React.FC<SignInModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  onError,
  onSwitchToSignUp
}) => {
  const [formData, setFormData] = useState<LoginFormData>({
    email: '',
    password: '',
    rememberMe: false
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});
  const [isLoading, setIsLoading] = useState(false);

  const { t } = useTranslation(['auth', 'common']);
  const { currentTheme, themeName } = useTheme();
  const router = useRouter();
  const { locale } = router;
  const isRTL = locale === 'ar';
  const isGoldTheme = themeName === 'gold';

  const handleInputChange = (field: keyof LoginFormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleInputBlur = (field: keyof LoginFormData) => {
    setTouched(prev => ({ ...prev, [field]: true }));
    validateField(field);
  };

  const validateField = (field: keyof LoginFormData) => {
    try {
      if (field === 'email') {
        userLoginSchema.shape.email.parse(formData.email);
      } else if (field === 'password') {
        userLoginSchema.shape.password.parse(formData.password);
      }
      setErrors(prev => ({ ...prev, [field]: '' }));
    } catch (error) {
      if (error instanceof z.ZodError) {
        setErrors(prev => ({ 
          ...prev, 
          [field]: t(`auth:validation.${error.errors[0].code}`) || error.errors[0].message 
        }));
      }
    }
  };

  const validateForm = (): boolean => {
    try {
      userLoginSchema.parse(formData);
      setErrors({});
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors: Record<string, string> = {};
        error.errors.forEach((err) => {
          if (err.path[0]) {
            newErrors[err.path[0] as string] = t(`auth:validation.${err.code}`) || err.message;
          }
        });
        setErrors(newErrors);
        setTouched({ email: true, password: true });
      }
      return false;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Mock success
      if (onSuccess) {
        onSuccess({ user: { email: formData.email }, token: 'mock-token' });
      }
      
      onClose();
    } catch (error) {
      const errorMessage = t('auth:login.networkError');
      setErrors({ general: errorMessage });
      if (onError) {
        onError(errorMessage);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const buttonClasses = `
    w-full h-14 rounded-xl font-semibold text-lg transition-all duration-300
    flex items-center justify-center gap-3 group relative overflow-hidden
    ${isRTL ? 'font-tajawal' : 'font-sans'}
    ${isLoading ? 'cursor-not-allowed opacity-70' : 'cursor-pointer'}
  `;

  const primaryButtonStyle = {
    background: isGoldTheme
      ? 'linear-gradient(135deg, rgba(251, 191, 36, 0.9) 0%, rgba(245, 158, 11, 0.9) 100%)'
      : 'linear-gradient(135deg, rgba(147, 51, 234, 0.9) 0%, rgba(126, 34, 206, 0.9) 100%)',
    backdropFilter: 'blur(20px)',
    WebkitBackdropFilter: 'blur(20px)',
    border: '1px solid rgba(255, 255, 255, 0.2)',
    boxShadow: isGoldTheme
      ? '0 8px 32px rgba(251, 191, 36, 0.3)'
      : '0 8px 32px rgba(147, 51, 234, 0.3)',
  };

  return (
    <AuthModal
      isOpen={isOpen}
      onClose={onClose}
      title={t('auth:login.title')}
      subtitle={t('auth:login.subtitle')}
      description={t('auth:login.description')}
      size="md"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* General Error */}
        {errors.general && (
          <motion.div
            className="p-4 rounded-xl bg-red-500/10 border border-red-500/20 text-red-400 text-center"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
          >
            {errors.general}
          </motion.div>
        )}

        {/* Email Input */}
        <AuthInput
          label={t('auth:login.email')}
          placeholder={t('auth:login.emailPlaceholder')}
          type="email"
          value={formData.email}
          onChange={(value) => handleInputChange('email', value)}
          onBlur={() => handleInputBlur('email')}
          error={errors.email}
          touched={touched.email}
          required
          autoComplete="email"
          leftIcon={<EnvelopeIcon className="w-5 h-5" />}
        />

        {/* Password Input */}
        <AuthInput
          label={t('auth:login.password')}
          placeholder={t('auth:login.passwordPlaceholder')}
          type="password"
          value={formData.password}
          onChange={(value) => handleInputChange('password', value)}
          onBlur={() => handleInputBlur('password')}
          error={errors.password}
          touched={touched.password}
          required
          autoComplete="current-password"
          leftIcon={<LockClosedIcon className="w-5 h-5" />}
        />

        {/* Remember Me & Forgot Password */}
        <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
          <label className="flex items-center gap-3 cursor-pointer group">
            <input
              type="checkbox"
              checked={formData.rememberMe}
              onChange={(e) => handleInputChange('rememberMe', e.target.checked)}
              className="w-4 h-4 rounded border-white/20 bg-white/10 text-current focus:ring-2 focus:ring-current"
            />
            <span className={`text-sm ${isRTL ? 'font-tajawal' : 'font-sans'}`}
                  style={{ color: currentTheme.colors.text.secondary }}>
              {t('auth:login.rememberMe')}
            </span>
          </label>

          <button
            type="button"
            className={`text-sm transition-colors duration-200 ${isRTL ? 'font-tajawal' : 'font-sans'}`}
            style={{ color: currentTheme.colors.text.accent }}
          >
            {t('auth:login.forgotPassword')}
          </button>
        </div>

        {/* Submit Button */}
        <motion.button
          type="submit"
          disabled={isLoading}
          className={buttonClasses}
          style={primaryButtonStyle}
          whileHover={!isLoading ? { scale: 1.02, y: -2 } : {}}
          whileTap={!isLoading ? { scale: 0.98 } : {}}
        >
          {isLoading ? (
            <>
              <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
              {t('auth:login.loggingIn')}
            </>
          ) : (
            <>
              {t('auth:login.loginButton')}
              {isRTL ? (
                <ArrowLeftIcon className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
              ) : (
                <ArrowRightIcon className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
              )}
            </>
          )}
        </motion.button>

        {/* Switch to Sign Up */}
        <div className={`text-center ${isRTL ? 'font-tajawal' : 'font-sans'}`}>
          <span style={{ color: currentTheme.colors.text.secondary }}>
            {t('auth:login.noAccount')}
          </span>
          {' '}
          <button
            type="button"
            onClick={onSwitchToSignUp}
            className="font-semibold transition-colors duration-200 hover:underline"
            style={{ color: currentTheme.colors.text.accent }}
          >
            {t('auth:login.signUp')}
          </button>
        </div>
      </form>
    </AuthModal>
  );
};

export default SignInModal;
