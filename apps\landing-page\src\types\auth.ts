export interface LoginFormData {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  password: string;
  confirmPassword: string;
  role: 'CLIENT' | 'EXPERT';
  governorate?: string;
  city?: string;
  language: 'ar' | 'en';
  acceptTerms: boolean;
}

export interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: (data: { user: { email: string; role?: string }; token: string }) => void;
  onError?: (error: string) => void;
}

export interface AuthFormState {
  isLoading: boolean;
  errors: Record<string, string>;
  touched: Record<string, boolean>;
}

export interface PasswordStrength {
  score: number;
  label: 'weak' | 'fair' | 'good' | 'strong';
  hasUppercase: boolean;
  hasLowercase: boolean;
  hasNumber: boolean;
  hasSpecialChar: boolean;
  hasMinLength: boolean;
}

export interface SyrianLocation {
  governorate: string;
  cities: string[];
}

export const SYRIAN_GOVERNORATES: SyrianLocation[] = [
  {
    governorate: 'دمشق',
    cities: ['دمشق', 'جرمانا', 'دوما', 'داريا', 'قدسيا', 'صحنايا']
  },
  {
    governorate: 'حلب',
    cities: ['حلب', 'منبج', 'عفرين', 'جرابلس', 'الباب', 'أعزاز']
  },
  {
    governorate: 'حمص',
    cities: ['حمص', 'تدمر', 'القريتين', 'الرستن', 'تلبيسة', 'صدد']
  },
  {
    governorate: 'حماة',
    cities: ['حماة', 'سلمية', 'مصياف', 'محردة', 'السقيلبية', 'قلعة المضيق']
  },
  {
    governorate: 'اللاذقية',
    cities: ['اللاذقية', 'جبلة', 'بانياس', 'القرداحة', 'الحفة', 'كسب']
  },
  {
    governorate: 'طرطوس',
    cities: ['طرطوس', 'بانياس', 'صافيتا', 'دريكيش', 'الشيخ بدر', 'القدموس']
  },
  {
    governorate: 'إدلب',
    cities: ['إدلب', 'جسر الشغور', 'أريحا', 'معرة النعمان', 'سراقب', 'خان شيخون']
  },
  {
    governorate: 'درعا',
    cities: ['درعا', 'إزرع', 'الصنمين', 'نوى', 'جاسم', 'الشيخ مسكين']
  },
  {
    governorate: 'السويداء',
    cities: ['السويداء', 'صلخد', 'شهبا', 'قنوات', 'المزرعة', 'عرى']
  },
  {
    governorate: 'القنيطرة',
    cities: ['القنيطرة', 'فيق', 'خان أرنبة', 'مسعدة', 'جباتا الخشب', 'عين التينة']
  },
  {
    governorate: 'الرقة',
    cities: ['الرقة', 'تل أبيض', 'الثورة', 'سلوك', 'كسرة', 'الجرنية']
  },
  {
    governorate: 'دير الزور',
    cities: ['دير الزور', 'الميادين', 'البوكمال', 'الأشارة', 'التيم', 'الصالحية']
  },
  {
    governorate: 'الحسكة',
    cities: ['الحسكة', 'القامشلي', 'رأس العين', 'المالكية', 'عامودا', 'الدرباسية']
  },
  {
    governorate: 'ريف دمشق',
    cities: ['التل', 'النبك', 'يبرود', 'القطيفة', 'الزبداني', 'مضايا']
  }
];
